package org.befun.core.generator;

import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Random;
import java.util.zip.CRC32;

/**
 * 分布式ID生成器，基于Twitter的Snowflake版本 (定制版本)
 *
 * JavaScript兼容版本：
 * - 总位数限制在53位以内，确保JavaScript Number类型可以安全处理
 * - 使用秒级时间戳而不是毫秒级，减少时间戳占用的位数
 * - 位分配：时间戳(32位) + 数据中心ID(3位) + 机器ID(3位) + 序列号(15位) = 53位
 *
 * <AUTHOR>
 */
public class Snowflake {
    /** 起始时间戳 2021年5月1日 GMT (秒级) */
    private static final Long START_TIME = 1619827200L;

    /** 机器id所占长度 */
    private static final int WORK_LEN = 3;

    /** 数据中心id所占长度 */
    private static final int DATA_LEN = 3;

    /** 序号所占长度 (增加到15位以补偿秒级时间戳的精度损失) */
    private static final int SEQUENCE_LEN = 15;

    /** 机器id最大值 7 (3位) */
    private static final int WORK_MAX_NUM = ~(-1 << WORK_LEN);

    /** 数据中心id最大值 7 (3位) */
    private static final int DATA_MAX_NUM = ~(-1 << DATA_LEN);

    /**
     * 工作机器ID(0~7)
     */
    private long workerId = 0L;

    /**
     * 数据中心ID(0~7)
     */
    private long datacenterId = 0L;

    /**
     * 秒内序列(0~32767)，15位可以支持每秒32768个ID
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间截(秒级)
     */
    private long lastTimestamp = -1L;

    /**
     * ctor: default worker and datacenterId
     */
    public Snowflake() {
        this.workerId = getWorkId();
        this.datacenterId = getDataId();
    }

    /**
     * ctor: default worker and datacenterId
     */
    public Snowflake(int workerId, int dataId) {
        this.workerId = workerId;
        this.datacenterId = dataId;
    }

    /**
     * 获取字符串s的字节数组，然后将数组的元素相加，对（max+1）取余
     */
    private static int getHostId(String s, int max){
        CRC32 crc = new CRC32();
        crc.update(s.getBytes(StandardCharsets.UTF_8));

        // Get the hash value and take modulus with maxValue
        long hash = crc.getValue();
        int uniqueId = (int) (hash % (max + 1)); // Ensure it's <= maxValue

        // Ensure the uniqueId is positive
        return Math.abs(uniqueId);
    }

    /**
     * 根据 host address 取余，发生异常就获取 0到7之间的随机数
     */
    public static int getWorkId(){
        try {
            return getHostId(Inet4Address.getLocalHost().getHostAddress(), WORK_MAX_NUM);
        } catch (UnknownHostException e) {
            return new Random().nextInt(WORK_MAX_NUM + 1);
        }
    }

    /**
     * 根据 host name 取余，发生异常就获取 0到7之间的随机数
     */
    public static int getDataId() {
        try {
            return getHostId(Inet4Address.getLocalHost().getHostName(), DATA_MAX_NUM);
        } catch (UnknownHostException e) {
            return new Random().nextInt(DATA_MAX_NUM + 1);
        }
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     *
     * JavaScript兼容版本：
     * - 使用秒级时间戳
     * - 总位数控制在53位以内
     * - 位分配：时间戳(32位) + 数据中心ID(3位) + 机器ID(3位) + 序列号(15位) = 53位
     *
     * @return SnowflakeId (53位以内，JavaScript安全)
     */
    public synchronized long nextId() {
        long timestamp = timeGen();
        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                    String.format("Clock moved backwards.  Refusing to generate id for %d seconds", lastTimestamp - timestamp));
        }
        // 如果是同一时间生成的，则进行秒内序列
        if (lastTimestamp == timestamp) {
            // 生成序列的掩码，15位可以支持32767个序列号 (0b111111111111111=0x7fff=32767)
            long sequenceMask = ~(-1L << SEQUENCE_LEN);
            sequence = (sequence + 1) & sequenceMask;
            // 秒内序列溢出
            if (sequence == 0) {
                // 阻塞到下一秒,获得新的时间戳
                timestamp = tilNextSecond(lastTimestamp);
            }
        }
        // 时间戳改变，秒内序列重置
        else {
            sequence = 0L;
        }

        // 上次生成ID的时间截
        lastTimestamp = timestamp;
        long datacenterIdShift = SEQUENCE_LEN + WORK_LEN;
        // 时间截向左移21位(15+3+3)
        long timestampLeftShift = SEQUENCE_LEN + WORK_LEN + DATA_LEN;

        long id = ((timestamp - START_TIME) << timestampLeftShift)
                | (datacenterId << datacenterIdShift)
                | (workerId << SEQUENCE_LEN)
                | sequence;

        // 验证生成的ID是否超过JavaScript最大安全整数
        if (id > 9007199254740991L) { // 2^53 - 1
            throw new RuntimeException("Generated ID exceeds JavaScript MAX_SAFE_INTEGER: " + id);
        }

        return id;
    }

    /**
     * 阻塞到下一秒，直到获得新的时间戳
     *
     * @param lastTimestamp 上次生成ID的时间截(秒)
     * @return 当前时间戳(秒)
     */
    private long tilNextSecond(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以秒为单位的当前时间
     *
     * @return 当前时间(秒)
     */
    private long timeGen() {
        return System.currentTimeMillis() / 1000;
    }

    public static void main(String[] args) {
        Snowflake snowflake = new Snowflake();
        System.out.println(snowflake.nextId());
    }
}
