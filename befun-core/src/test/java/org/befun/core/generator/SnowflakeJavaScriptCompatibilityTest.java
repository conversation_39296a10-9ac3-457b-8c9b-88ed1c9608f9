package org.befun.core.generator;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;

/**
 * 测试修复后的Snowflake生成的ID是否在JavaScript安全范围内
 * JavaScript中Number.MAX_SAFE_INTEGER = 2^53 - 1 = 9007199254740991
 *
 * <AUTHOR>
 */
public class SnowflakeJavaScriptCompatibilityTest {

    /**
     * JavaScript中Number类型的最大安全整数值
     * Number.MAX_SAFE_INTEGER = 2^53 - 1 = 9007199254740991
     */
    private static final long JS_MAX_SAFE_INTEGER = 9007199254740991L;
    
    @Test
    public void testFixedSnowflakeWithinJavaScriptSafeRange() {
        Snowflake snowflake = new Snowflake();

        // 生成多个ID进行测试
        for (int i = 0; i < 100; i++) {
            long id = snowflake.nextId();
            int bitLength = Long.toBinaryString(id).length();

            // 验证ID不超过JavaScript的最大安全整数
            Assertions.assertTrue(id <= JS_MAX_SAFE_INTEGER,
                "Generated ID " + id + " exceeds JavaScript MAX_SAFE_INTEGER " + JS_MAX_SAFE_INTEGER);

            // 验证位长度不超过53位
            Assertions.assertTrue(bitLength <= 53,
                "Generated ID bit length " + bitLength + " exceeds 53 bits");

            // 验证ID为正数
            Assertions.assertTrue(id > 0, "Generated ID should be positive");

            System.out.println("ID: " + id + ", Bit length: " + bitLength + " ✓");
        }
    }
    
    @Test
    public void analyzeFixedSnowflakeBitStructure() {
        Snowflake snowflake = new Snowflake();
        long id = snowflake.nextId();

        System.out.println("Generated ID: " + id);
        System.out.println("Binary representation: " + Long.toBinaryString(id));
        System.out.println("Bit length: " + Long.toBinaryString(id).length());
        System.out.println("JS MAX_SAFE_INTEGER binary: " + Long.toBinaryString(JS_MAX_SAFE_INTEGER));
        System.out.println("JS MAX_SAFE_INTEGER bit length: " + Long.toBinaryString(JS_MAX_SAFE_INTEGER).length());

        // 分析当前时间戳会产生多大的ID (秒级)
        long currentTime = System.currentTimeMillis() / 1000;
        long startTime = 1619827200L; // 2021年5月1日 GMT (秒级)
        long timeDiff = currentTime - startTime;

        System.out.println("Current timestamp (seconds): " + currentTime);
        System.out.println("Start timestamp (seconds): " + startTime);
        System.out.println("Time difference (seconds): " + timeDiff);

        // 计算最大可能的ID (所有位都设为最大值)
        // 新的位分配：时间戳(32位) + 数据中心ID(3位) + 机器ID(3位) + 序列号(15位) = 53位
        long maxDatacenterId = 7;     // 3 bits = 2^3 - 1 = 7
        long maxWorkerId = 7;         // 3 bits = 2^3 - 1 = 7
        long maxSequence = 32767;     // 15 bits = 2^15 - 1 = 32767

        long maxPossibleId = (timeDiff << 21) | (maxDatacenterId << 18) | (maxWorkerId << 15) | maxSequence;

        System.out.println("Max possible ID with current timestamp: " + maxPossibleId);
        System.out.println("Max possible ID bit length: " + Long.toBinaryString(maxPossibleId).length());
        System.out.println("Max possible ID exceeds JS limit: " + (maxPossibleId > JS_MAX_SAFE_INTEGER));

        // 验证最大可能ID不超过JavaScript限制
        Assertions.assertTrue(maxPossibleId <= JS_MAX_SAFE_INTEGER,
            "Max possible ID should not exceed JavaScript MAX_SAFE_INTEGER");
    }
    
    @Test
    public void calculateFixedSnowflakeTimeRange() {
        // 修复后的Snowflake结构: [timestamp(32位)] [datacenter(3位)] [worker(3位)] [sequence(15位)]
        // 总共53位，完全符合JavaScript限制

        long startTime = 1619827200L; // 2021年5月1日 GMT (秒级)

        // 32位时间戳可以表示的最大秒数
        long maxTimestamp32Bit = (1L << 32) - 1;
        long maxAllowedTime = startTime + maxTimestamp32Bit;

        System.out.println("32-bit timestamp max value: " + maxTimestamp32Bit + " seconds");
        System.out.println("Max allowed time: " + maxAllowedTime);
        System.out.println("Max allowed date: " + new java.util.Date(maxAllowedTime * 1000));
        System.out.println("Current time: " + (System.currentTimeMillis() / 1000));
        System.out.println("Current date: " + new java.util.Date());

        // 计算可用年限
        long availableYears = maxTimestamp32Bit / (365 * 24 * 3600);
        System.out.println("Available years: " + availableYears + " years");

        // 检查当前是否已经超过
        long currentTime = System.currentTimeMillis() / 1000;
        if (currentTime > maxAllowedTime) {
            System.out.println("ALERT: Current time already exceeds the 32-bit timestamp limit!");
            Assertions.fail("Current time exceeds 32-bit timestamp limit");
        } else {
            long remainingTime = maxAllowedTime - currentTime;
            long remainingYears = remainingTime / (365 * 24 * 3600);
            System.out.println("Remaining time before exceeding 32-bit limit: " + remainingYears + " years");

            // 验证至少还有100年可用
            Assertions.assertTrue(remainingYears > 100,
                "Should have at least 100 years remaining, but only " + remainingYears + " years left");
        }
    }

    @Test
    public void testHighConcurrencyScenario() {
        Snowflake snowflake = new Snowflake();

        // 测试在同一秒内生成大量ID
        for (int i = 0; i < 1000; i++) {
            long id = snowflake.nextId();

            // 验证每个ID都在JavaScript安全范围内
            Assertions.assertTrue(id <= JS_MAX_SAFE_INTEGER,
                "ID " + id + " exceeds JavaScript MAX_SAFE_INTEGER");

            // 验证ID唯一性（简单检查：ID应该递增）
            if (i > 0) {
                // 由于使用秒级时间戳，同一秒内的ID应该通过序列号递增
                // 这里不做严格的递增检查，因为可能跨秒
            }
        }

        System.out.println("✅ High concurrency test passed: 1000 IDs generated successfully");
    }
}
