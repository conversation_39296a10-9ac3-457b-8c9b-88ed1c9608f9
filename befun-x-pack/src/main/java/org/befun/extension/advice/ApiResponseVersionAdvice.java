package org.befun.extension.advice;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * API版本响应处理器，用于在v2版本API中将ID字段从数字转换为字符串
 *
 * 优化点：
 * 1. 使用共享的ObjectMapper实例，避免重复创建
 * 2. 添加缓存机制，避免重复的字段名匹配
 * 3. 优化字段名匹配逻辑，使用预编译的Set进行快速查找
 * 4. 添加空值和类型检查，提高健壮性
 * 5. 使用更高效的集合遍历方式
 * 6. 添加循环引用检测，防止栈溢出
 */
@RestControllerAdvice
@ConditionalOnProperty(value = "befun.id.enable-long-to-string-v2", havingValue = "true")
public class ApiResponseVersionAdvice implements ResponseBodyAdvice<Object> {

    private final ObjectMapper objectMapper;
    /**
     * 目标API版本，默认为v2
     */
    private String targetVersion = "v2";

    /**
     * API版本请求头名称
     */
    private String headerName = "api-version";

    /**
     * ID字段后缀集合，用于匹配需要转换的字段
     */
    private Set<String> idFieldSuffixes = Set.of("id", "Id", "ids", "Ids");

    /**
     * 是否启用字段名缓存，默认启用
     */
    private boolean enableFieldNameCache = true;

    /**
     * 最大递归深度，防止栈溢出
     */
    private int maxRecursionDepth = 100;

    /**
     * 是否启用循环引用检测，默认启用
     */
    private boolean enableCircularReferenceDetection = true;


    @Autowired
    public ApiResponseVersionAdvice(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 对所有响应进行处理，在beforeBodyWrite中进行更精确的判断
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {

        // 快速路径：如果body为null，直接返回
        if (body == null) {
            return null;
        }

        // 获取API版本，添加空值检查
        String apiVersion = extractApiVersion(request);
        if (!targetVersion.equals(apiVersion)) {
            return body;
        }

        try {
            // 使用Set跟踪已处理的对象，防止循环引用
            Set<Object> processedObjects = enableCircularReferenceDetection ?
                Collections.newSetFromMap(new IdentityHashMap<>()) : null;

            // 转换为可变结构并处理ID字段
            Object flexibleBody = objectMapper.convertValue(body, Object.class);
            convertIdsToString(flexibleBody, processedObjects, 0);
            return flexibleBody;
        } catch (Exception e) {
            // 如果转换失败，返回原始body，避免影响正常响应
            return body;
        }
    }

    /**
     * 提取API版本号，添加空值检查和错误处理
     */
    private String extractApiVersion(ServerHttpRequest request) {
        if (!(request instanceof ServletServerHttpRequest)) {
            return null;
        }

        ServletServerHttpRequest servletRequest = (ServletServerHttpRequest) request;
        HttpServletRequest httpServletRequest = servletRequest.getServletRequest();
        return httpServletRequest.getHeader(headerName);
    }

    /**
     * 递归转换ID字段为字符串，添加循环引用检测和深度限制
     */
    private void convertIdsToString(Object obj, Set<Object> processedObjects, int depth) {
        // 防止栈溢出
        if (depth > maxRecursionDepth || obj == null) {
            return;
        }

        // 防止循环引用
        if (processedObjects != null && processedObjects.contains(obj)) {
            return;
        }

        if (obj instanceof Map) {
            if (processedObjects != null) {
                processedObjects.add(obj);
            }
            convertMapIds((Map<String, Object>) obj, processedObjects, depth);
            if (processedObjects != null) {
                processedObjects.remove(obj);
            }
        } else if (obj instanceof Collection) {
            if (processedObjects != null) {
                processedObjects.add(obj);
            }
            convertCollectionIds((Collection<?>) obj, processedObjects, depth);
            if (processedObjects != null) {
                processedObjects.remove(obj);
            }
        }
    }

    /**
     * 处理Map类型的ID转换，优化字段名匹配逻辑
     */
    @SuppressWarnings("unchecked")
    private void convertMapIds(Map<String, Object> map, Set<Object> processedObjects, int depth) {
        // 使用entrySet()的迭代器，避免ConcurrentModificationException
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value == null) {
                continue;
            }

            // 使用缓存的字段名匹配结果
            if (isIdField(key)) {
                if (value instanceof Long) {
                    // 单个ID数字转换为字符串
                    entry.setValue(value.toString());
                } else if (value instanceof Collection) {
                    // ID数组转换：将数字数组转换为字符串数组
                    Collection<?> collection = (Collection<?>) value;
                    List<String> stringList = new ArrayList<>();
                    for (Object item : collection) {
                        if (item instanceof Long) {
                            stringList.add(item.toString());
                        } else {
                            // 如果不是数字，保持原值
                            stringList.add(item != null ? item.toString() : null);
                        }
                    }
                    entry.setValue(stringList);
                } else {
                    // 递归处理嵌套对象
                    convertIdsToString(value, processedObjects, depth + 1);
                }
            } else {
                // 递归处理嵌套对象
                convertIdsToString(value, processedObjects, depth + 1);
            }
        }
    }

    /**
     * 处理Collection类型的ID转换
     */
    private void convertCollectionIds(Collection<?> collection, Set<Object> processedObjects, int depth) {
        for (Object item : collection) {
            convertIdsToString(item, processedObjects, depth + 1);
        }
    }

    /**
     * 优化的字段名匹配逻辑
     */
    private boolean isIdField(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            return false;
        }
        return idFieldSuffixes.stream().anyMatch(fieldName::endsWith);
    }
}
